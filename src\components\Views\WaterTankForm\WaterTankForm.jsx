import React, { useContext, useState, useEffect, useCallback } from 'react';
import { UserContext } from '../../../context/UserProvider';
import {
  Divider,
  Grid,
  Button,
  makeStyles,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import { withRouter } from 'react-router-dom/cjs/react-router-dom.min';
import { ConfirmationAlerts } from '../IrrigationForm/components/ConfirmationAlerts';
import WaterTankCollapsibleTable from './WaterTankCollapsibleTable';
import AddWaterTankDialog from './AddWaterTankDialog';
import { db } from '../../../config/firebase';
// moment-timezone is used in WaterTankCollapsibleTable for formatting dates


const useStyles = makeStyles((theme) => ({
  addButton: {
    margin: theme.spacing(2),
  },
  headerContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  title: {
    flexGrow: 1,
  },
}));

const WaterTankForm = () => {
  const classes = useStyles();
  const { currentMac, canIdIrrigation, usuario, togglesNames, togglesUid,
    switchesUid, levelSensorsUid, levelSensorsNames } = useContext(UserContext);

  // Estados para alertas y datos
  const [open, setOpen] = useState(false);
  const [typeAlert, setTypeAlert] = useState("info");
  const [alertMessage, setAlertMessage] = useState("");
  const [alertTitle, setAlertTitle] = useState("");
  const [waterTanks, setWaterTanks] = useState([]);
  const [showTable, setShowTable] = useState(false);

  // Estados para el diálogo de agregar/editar tanque
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingTank, setEditingTank] = useState(null);

  const handleAlert = useCallback((openValue, type, title = "", message = "") => {
    setOpen(openValue);
    setTypeAlert(type);

    if (type === "success") {
      setAlertTitle(title || "¡Éxito!");
      setAlertMessage(message || "La operación se ha realizado con éxito");
    } else if (type === "error") {
      setAlertTitle(title || "Error");
      setAlertMessage(message || "Ha ocurrido un error al realizar la operación");
    } else if (type === "warning") {
      setAlertTitle(title || "Advertencia");
      setAlertMessage(message || "Tenga precaución al realizar esta operación");
    } else if (type === "info") {
      setAlertTitle(title || "Información");
      setAlertMessage(message || "Operación en proceso");
    }
  }, [setOpen, setTypeAlert, setAlertTitle, setAlertMessage]);

  // Cargar datos de los tanques de agua
  useEffect(() => {
    const fetchWaterTanks = async () => {
      if (usuario && usuario.username && currentMac && canIdIrrigation) {
        try {
          // Aquí se obtendría la información de los tanques desde Firebase
          // console.log("Esto es togglesNames:", togglesNames)
          // console.log("Esto es togglesUid:", togglesUid)
          const waterTankAddr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/waterTankControl`;
          const waterDOcRef = db.collection(waterTankAddr).doc("tankRows");
          const snapshot = await waterDOcRef.get();
          let mockTanks = [];
          if(snapshot.exists) {
            const data = snapshot.data();
            const rowData = data.allRows;

            const tanksData = rowData.map((row, index) => {
              const fillData = row.fill
              const emptyData = row.empty
              const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump)
              const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump)
              const fillingPump = togglesNames[indexFPump]
              const emptyingPump = togglesNames[indexEPump]
              return {
              id: index,
              name: row.name,
              action: row.lastAction,
              lastExecution: row.lastExecution,
              waterLevelSensor: row.levelSensor,
              recirculationValve: row.recirculationValve,
              details: {
                "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
                "Bomba de llenado": fillingPump,
                "Bomba de vaciado": emptyingPump,
              },
              fillData: fillData,
              emptyData: emptyData
              }
            });
            mockTanks = tanksData;
          }

          setWaterTanks(mockTanks);
          setShowTable(true);
        } catch (error) {
          console.error("Error al cargar los tanques de agua:", error);
          handleAlert(true, "error", "Error", "No se pudieron cargar los datos de los tanques");
        }
      }
    };

    fetchWaterTanks();
  }, [usuario, currentMac, canIdIrrigation, handleAlert,togglesNames,togglesUid]);

  // Función para obtener el nivel actual de agua de un tanque
  const getWaterLevelData = async (sensorUid) => {
    if (!sensorUid) {
      console.log("No hay sensor de nivel configurado para este tanque");
      return null;
    }

    try {
      // Extraer el nodeId y sensorId del UID del sensor (formato: nodeId@sensorId)
      const sensorParts = sensorUid.split('@');
      if (sensorParts.length < 4) {
        console.error("Formato de UID de sensor inválido:", sensorUid);
        return null;
      }

      const nodeId = sensorParts[2];
      const sensorId = sensorParts[3];

      // Obtener los datos del sensor
      const docPath = `${usuario.username}/loraDevices/nodes/${nodeId}/sensors/dataSensors`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el sensor:", sensorUid);
        return null;
      }

      const sensorData = docRef.data();
      if (!sensorData || !sensorData.data) {
        console.error("Estructura de datos inválida para el sensor:", sensorUid);
        return null;
      }

      // Buscar el sensor específico en el array de datos
      const sensorInfo = sensorData.data.find(sensor => sensor.id === Number(sensorId));
      if (!sensorInfo) {
        console.error("No se encontró información para el sensor ID:", sensorId);
        return null;
      }

      // Determinar el valor a usar según el tipo de medición
      let waterLevel;
      waterLevel = sensorInfo.percentageWater;

      return waterLevel;
    } catch (error) {
      console.error("Error al obtener datos del nivel de agua:", error);
      return null;
    }
  };

  // Función para obtener el estado actual de un switch específico
  const getSwitchState = async (switchUid) => {
    if (!switchUid || !usuario || !usuario.username) {
      console.log("No hay switch configurado o usuario no disponible");
      return null;
    }

    try {
      // Extraer el nodeId, canId y outId del UID del switch (formato: macId@canId@kind@outId)
      const switchParts = switchUid.split('@');
      if (switchParts.length < 4) {
        console.error("Formato de UID de switch inválido:", switchUid);
        return null;
      }

      const macId = switchParts[0];
      const canId = switchParts[1];
      const outId = switchParts[3];

      // Obtener los datos del switch desde Firebase
      const docPath = `${usuario.username}/infoDevices/${macId}/${canId}/fromModule/render`;
      const docRef = await db.doc(docPath).get();

      if (!docRef.exists) {
        console.error("No se encontraron datos para el switch:", switchUid);
        return null;
      }

      const switchData = docRef.data().C_bool;
      const resp = switchData[Number(outId)];

      return resp;
    } catch (error) {
      console.error("Error al obtener el estado del switch:", error);
      return null;
    }
  };

  // Función para calcular el tiempo estimado basado en el nivel de agua
  const calculateEstimatedTime = (waterLevel, totalMinutes, totalSeconds, action) => {
    if (!waterLevel) return null;

    // Convertir el tiempo total a segundos
    const totalTimeInSeconds = (parseInt(totalMinutes) || 0) * 60 + (parseInt(totalSeconds) || 0);
    if (totalTimeInSeconds <= 0) return null;

    // Calcular el tiempo estimado basado en el porcentaje de agua
    const percentage = waterLevel;
    let remainingPercentage;

    if (action === "fill") {
      // Si estamos llenando, el tiempo restante es proporcional al porcentaje que falta llenar
      remainingPercentage = 100 - percentage;
    } else {
      // Si estamos vaciando, el tiempo restante es proporcional al porcentaje actual
      remainingPercentage = percentage;
    }

    // Calcular el tiempo estimado en segundos
    const estimatedTimeInSeconds = (totalTimeInSeconds * remainingPercentage) / 100;

    // Convertir a minutos y segundos
    const estimatedMinutes = Math.floor(estimatedTimeInSeconds / 60);
    const estimatedSeconds = Math.floor(estimatedTimeInSeconds % 60);

    return {
      minutes: estimatedMinutes,
      seconds: estimatedSeconds,
      totalSeconds: estimatedTimeInSeconds
    };
  };

  // Función para enviar la rutina de los tanques por MQTT
  const sendingTankRoutine = async (e, n) => {
        const item = {
            msMqtt: e,
            mac: currentMac,
            action: "Tank Routine",
            fecha: Date.now(),
            uid: n
        }

        try {
            const addr = `${usuario.username}/infoDevices/${currentMac}/${canIdIrrigation}/sendConfigModule`
            await db.collection(addr).doc("sendConfig").set({ item })
            //console.log("Esto es macId:", macId)
        } catch (error) {
            console.log(error)
        }
      }

  // Manejar la ejecución de una acción en un tanque
  const handleExecuteTank = async (tank, completed = false, stoppedBySwitch = false) => {
    // Verificamos si el tanque ya está en ejecución (esto lo determinará el componente WaterTankCollapsibleTable)
    // Si el componente WaterTankCollapsibleTable cambia executingTankId a null, significa que estamos deteniendo
    // Si lo cambia al ID del tanque, significa que estamos ejecutando
    const moduleId = currentMac + "@" + canIdIrrigation;

    // Verificamos si hay un tanque en ejecución consultando el estado del botón en la tabla
    const isExecuting = document.querySelector(`button[data-tank-id="${tank.id}"]`)?.textContent === 'Detener';

    if (isExecuting && !completed) {
      // Si estamos deteniendo la acción y no ha terminado la operación
      // Verificamos si la detención fue por acción del usuario o por un cambio en el Switch
      if (stoppedBySwitch) {
        // Si la detención fue por un cambio en el Switch
        const actionText = tank.action === "fill" ? "llenado" : "vaciado";
        const switchType = tank.action === "fill" ? "máximo" : "mínimo";
        handleAlert(true, "success", "Operación completada", `La operación de ${actionText} en ${tank.name} ha sido completada automáticamente porque se activó el sensor de nivel ${switchType}.`);
      } else {
        // Si la detención fue por acción del usuario
        handleAlert(true, "warning", "Operación detenida", `Se ha detenido la acción ${tank.action} en ${tank.name} por acción del usuario.`);
      }

      let mqtt = "";
      const len = 35;
      const action = 244;
      const typeAction = 2;
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;
      const estimatedMinutes = 0;
      const estimatedSeconds = 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const pumpIndex = togglesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.pump : emptyData.pump));
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));

      const allOuts = Array(24).fill(0)

      mqtt =
            len +
            "," +
            action +
            "," +
            canIdIrrigation +
            "," +
            typeAction +
            "," +
            tank.id +
            "," +
            switchIndex +
            "," +
            estimatedMinutes +
            "," +
            estimatedSeconds +
            "," +
            totalMinutes +
            "," +
            totalSeconds +
            "," +
            minutesWaiting +
            "," +
            pumpIndex +
            "," +
            recirculationValve +
            "," +
            allOuts;
        console.log("Esto es mqtt:", mqtt);
        // sendingTankRoutine(mqtt, moduleId);

    } else if (!completed) {
      // Si estamos iniciando la acción y no ha terminado la operación, primero verificamos el estado de los switches de seguridad
      const fillData = tank.fillData;
      const emptyData = tank.emptyData;

      // Obtener los UIDs de los switches de nivel máximo y mínimo
      const maxLevelSwitchUid = fillData.maxSwitch;
      const minLevelSwitchUid = emptyData.minSwitch;

      // Verificar el estado de los switches según la acción a realizar
      if (tank.action === "fill") {
        // Para llenado, verificar que el switch de nivel máximo no esté activado (1)
        if (maxLevelSwitchUid) {
          const maxSwitchState = await getSwitchState(maxLevelSwitchUid);

          if (maxSwitchState === "1") {
            // Si el switch de nivel máximo está activado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el llenado en ${tank.name} porque el sensor de nivel máximo está activado.`
            );
            return; // Salir de la función sin ejecutar la acción
          }
        }
      } else if (tank.action === "empty") {
        // Para vaciado, verificar que el switch de nivel mínimo no esté desactivado (0)
        if (minLevelSwitchUid) {
          const minSwitchState = await getSwitchState(minLevelSwitchUid);

          if (minSwitchState === "0") {
            // Si el switch de nivel mínimo está desactivado, mostrar alerta y no ejecutar la acción
            handleAlert(
              true,
              "warning",
              "Operación no permitida",
              `No se puede ejecutar el vaciado en ${tank.name} porque el sensor de nivel mínimo está desactivado.`
            );
            return; // Salir de la función sin ejecutar la acción
          }
        }

        // Verificar el nivel de agua del tanque de destino (targetTank) para la acción de vaciar
        const targetTankUid = emptyData.targetTankWaterLevelS;
        const targetTankIndex = levelSensorsUid.findIndex(uid => uid === targetTankUid);
        const targetTankName = targetTankUid !== null ? levelSensorsNames[targetTankIndex] : null;
        if (targetTankUid !== null) {
          try {
            // Obtener el nivel actual de agua del tanque de destino
            const targetWaterLevel = await getWaterLevelData(targetTankUid);

            // Si el nivel de agua es mayor al 15%, no permitir la acción
            if (targetWaterLevel && (targetWaterLevel > 60 && targetWaterLevel !== -1 )) {
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el vaciado en ${tank.name} porque el tanque de destino "${targetTankName}" está demasiado lleno (${targetWaterLevel}%). El nivel debe ser menor o igual al 15%.`
              );
              return; // Salir de la función sin ejecutar la acción
            } else if(targetWaterLevel && targetWaterLevel === -1) {
              handleAlert(
                true,
                "warning",
                "Operación no permitida",
                `No se puede ejecutar el vaciado en ${tank.name} porque se desconoce el nivel del tanque de destino "${targetTankName}". Es necesario configurar los datos del sensor de nivel en el tanque de destino.`
              );
              return; // Salir de la función sin ejecutar la acción
            }
          } catch (error) {
            console.error("Error al verificar el nivel del tanque de destino:", error);
            // En caso de error, continuamos con la operación pero registramos el error
          }

        }
      }

      let estimatedMinutes = 0;
      let estimatedSeconds = 0;
      // Si estamos iniciando la acción, primero consultamos el nivel de agua actual
      if (tank.waterLevelSensor) {
        try {
          // Obtener el nivel actual de agua
          const waterLevel = await getWaterLevelData(tank.waterLevelSensor);
          const waterLevelSensorIndex = levelSensorsUid.findIndex(uid => uid === tank.waterLevelSensor);
          const waterLevelSensorName = levelSensorsNames[waterLevelSensorIndex];
          if (waterLevel && waterLevel !== -1) {
            // Calcular el tiempo estimado basado en el nivel de agua
            const actionData = tank.action === "fill" ? tank.fillData : tank.emptyData;
            const totalMinutes = actionData.minutes || 0;
            const totalSeconds = actionData.seconds || 0;

            const estimated = calculateEstimatedTime(waterLevel, totalMinutes, totalSeconds, tank.action);
            if (estimated) {
              estimatedMinutes = estimated.minutes;
              estimatedSeconds = estimated.seconds;
            }

            // Mostrar alerta con la información del nivel y tiempo estimado
            let timeInfo = '';
            if (estimated) {
              timeInfo = `${estimated.minutes} min ${estimated.seconds} seg`;
            } else {
              timeInfo = 'No disponible';
            }

            const actionText = tank.action === "fill" ? "llenado" : "vaciado";
            handleAlert(
              true,
              "info",
              "Operación en proceso",
              `Ejecutando ${actionText} en ${tank.name}.\nNivel actual: ${waterLevel}%.\nTiempo estimado: ${timeInfo}.`
            );
          } else {
            // Si no se pudo obtener el nivel, mostrar alerta genérica
            handleAlert(true, "warning",
              "No se pudo enviar la acción",
              `La acción ${tank.action} en ${tank.name} no se pudo completar debido a que no se tiene informacion del sensor de nivel ${waterLevelSensorName}.`);
          }
        } catch (error) {
          console.error("Error al obtener el nivel de agua:", error);
          handleAlert(true, "error", "No se pudo obtener el nivel de agua", `No se pudo obtener el nivel de agua asociado al tanque ${tank.name}`);
        }
      } else {
        // Si no hay sensor de nivel configurado, mostrar alerta genérica
        handleAlert(true, "warining", "No hay sensor de nivel configurado", `No se puede ejecutar la acción ${tank.action} en ${tank.name}`);
      }

      // Preparar y enviar el comando MQTT
      let mqtt = "";
      const len = 35;
      const action = 244;
      const typeAction = tank.action === "fill" ? 1 : 0;
      const totalMinutes = tank.action === "fill" ? fillData.minutes : emptyData.minutes;
      const totalSeconds = tank.action === "fill" ? fillData.seconds : emptyData.seconds;
      const minutesWaiting = tank.action === "fill" ? 0 : emptyData.minutesWaiting;

      const extraValves = tank.action === "fill" ? fillData.valveOutlets : emptyData.valveOutlets;
      const recirculationValve = togglesUid.findIndex(uid => uid === tank.recirculationValve);
      const pumpIndex = togglesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.pump : emptyData.pump));
      const switchIndex = switchesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.maxSwitch : emptyData.minSwitch));
      const waterSourceDestinyIndex = togglesUid.findIndex(uid => uid === (tank.action === "fill" ? fillData.waterSource : emptyData.targetTank));
      const extraValvesIndexes = extraValves.map(uid => togglesUid.findIndex(toggleUid => toggleUid === uid));

      const allOuts = Array(24).fill(0)
      allOuts[pumpIndex] = 1;
      allOuts[waterSourceDestinyIndex] = 1;
      extraValvesIndexes.forEach(index => {
        allOuts[index] = 1;
      });

      mqtt =
            len +
            "," +
            action +
            "," +
            canIdIrrigation +
            "," +
            typeAction +
            "," +
            tank.id +
            "," +
            switchIndex +
            "," +
            estimatedMinutes +
            "," +
            estimatedSeconds +
            "," +
            totalMinutes +
            "," +
            totalSeconds +
            "," +
            minutesWaiting +
            "," +
            pumpIndex +
            "," +
            recirculationValve +
            "," +
            allOuts;
      console.log("Esto es mqtt:", mqtt);
      // sendingTankRoutine(mqtt, moduleId);

      // Actualizar el último tiempo de ejecución
      const updatedTanks = waterTanks.map(t => {
        if (t.id === tank.id) {
          return { ...t, lastExecution: new Date().toISOString() };
        }
        return t;
      });

      setWaterTanks(updatedTanks);
    }
  };

  // Manejar la edición de un tanque
  const handleEditTank = (tank) => {
    // Si el clic viene del selector de acción, solo actualizamos la acción
    if (tank.action) {
      handleAlert(true, "info", "Acción cambiada", `Acción cambiada a ${tank.action === "fill" ? "Llenar" : "Vaciar"} para ${tank.name}`);

      setWaterTanks(prevTanks => {
        const updatedTanks = prevTanks.map(t => {
          if (t.id === tank.id) {
            return { ...t, action: tank.action };
          }
          return t;
        });
        return updatedTanks;
      });
    } else {
      // Si el clic viene del botón de editar, abrimos el diálogo de edición
      handleAlert(true, "info", "Edición", `Editando configuración de ${tank.name}`);

      // Establecer el tanque que se está editando y abrir el diálogo
      setEditingTank(tank);
      setOpenAddDialog(true);
    }
  };

  // Manejar la eliminación de un tanque
  const handleDeleteTank = (tank) => {
    handleAlert(true, "warning", "Confirmación", `¿Está seguro de eliminar ${tank.name}?`);
    // Aquí iría la lógica para confirmar y eliminar el tanque
  };

  // Abrir el diálogo para agregar un nuevo tanque
  const handleOpenAddDialog = () => {
    setOpenAddDialog(true);
  };

  // Cerrar el diálogo para agregar/editar un tanque
  const handleCloseAddDialog = () => {
    setOpenAddDialog(false);
    setEditingTank(null); // Limpiar el tanque en edición
  };

  // Manejar el éxito al agregar o actualizar un tanque
  const handleAddSuccess = (allRows, isEditing = false) => {
    handleCloseAddDialog();

    if (isEditing) {
      handleAlert(true, "success", "Tanque actualizado", "El tanque se ha actualizado correctamente");
    } else {
      handleAlert(true, "success", "Tanque agregado", "El tanque se ha agregado correctamente");
    }

    // Transformar los datos de Firebase al formato esperado por WaterTankCollapsibleTable
    const formattedTanks = allRows.map((row, index) => {
      const fillData = row.fill;
      const emptyData = row.empty;
      const indexFPump = togglesUid.findIndex(uid => uid === fillData.pump);
      const indexEPump = togglesUid.findIndex(uid => uid === emptyData.pump);
      const fillingPump = indexFPump !== -1 ? togglesNames[indexFPump] : "No encontrado";
      const emptyingPump = indexEPump !== -1 ? togglesNames[indexEPump] : "No encontrado";

      return {
        id: index,
        name: row.name,
        action: row.lastAction,
        lastExecution: row.lastExecution,
        waterLevelSensor: row.levelSensor,
        recirculationValve: row.recirculationValve,
        details: {
          "Ultima acción": row.lastAction === "fill" ? "Llenado" : "Vaciado",
          "Bomba de llenado": fillingPump,
          "Bomba de vaciado": emptyingPump,
        },
        fillData: fillData,
        emptyData: emptyData
      };
    });

    setWaterTanks(formattedTanks);
    setShowTable(true);
  };

  return (
    <>
      <div className={classes.headerContainer}>
        <Grid
          container
          justifyContent="space-between"
          alignItems="center"
        >
          <Grid item className={classes.title}>
            <h2>Control de Tanques de Agua</h2>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              className={classes.addButton}
              onClick={handleOpenAddDialog}
            >
              Agregar
            </Button>
          </Grid>
        </Grid>
      </div>
      <Divider />

      <div style={{ marginTop: '20px', marginBottom: '20px' }}>
        {showTable && (
          <WaterTankCollapsibleTable
            tanks={waterTanks}
            onExecute={handleExecuteTank}
            onEdit={handleEditTank}
            onDelete={handleDeleteTank}
          />
        )}
      </div>

      {/* Diálogo para agregar o editar un tanque */}
      <AddWaterTankDialog
        open={openAddDialog}
        onClose={handleCloseAddDialog}
        onSuccess={handleAddSuccess}
        editingTank={editingTank}
      />

      <div>
        <ConfirmationAlerts
          open={open}
          setOpen={setOpen}
          typeAlert={typeAlert}
          message={alertMessage}
          alertTitle={alertTitle}
        />
      </div>
    </>
  );
};

export default withRouter(WaterTankForm);
export { WaterTankForm };
